/**
 * 测试修复的TypeScript错误
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('开始测试TypeScript编译...');

try {
  // 尝试编译特定文件
  const files = [
    'src/visual-script/nodes/mocap/VirtualInteractionNode.ts',
    'src/visual-script/nodes/scene/SceneGenerationNodes.ts', 
    'src/visual-script/registry/NodeRegistry.ts'
  ];

  for (const file of files) {
    console.log(`检查文件: ${file}`);
    try {
      execSync(`node node_modules/typescript/bin/tsc --noEmit ${file}`, {
        cwd: __dirname,
        stdio: 'pipe'
      });
      console.log(`✓ ${file} 编译成功`);
    } catch (error) {
      console.log(`✗ ${file} 编译失败:`);
      console.log(error.stdout?.toString() || error.message);
    }
  }

  console.log('\n测试完成！');
} catch (error) {
  console.error('测试过程中出现错误:', error.message);
}
